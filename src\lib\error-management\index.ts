/**
 * Unified Error Management System - 2025 Optimized
 * 
 * Centralized exports for all error handling utilities
 * Consolidates and replaces scattered error handling patterns
 */

// Core error handler
export {
  UnifiedErrorHandler,
  errorHandler,
  handleError,
  handleCacheError,
  handleRealtimeError,
  handleFormValidationError,
  showSuc<PERSON>,
  showInfo,
  ERROR_CODES,
  type UnifiedError,
  type ErrorCode,
  type ValidationErrorDetail,
  type HttpErrorDetail,
  type NetworkErrorDetail,
  type ClerkErrorDetail,
  type ErrorDetails,
} from './unified-error-handler';

// Error boundaries
export {
  UnifiedErrorBoundary,
  useUnifiedErrorHandler,
  withUnifiedErrorBoundary,
  PageErrorBoundary,
  SectionErrorBoundary,
  ComponentErrorBoundary,
  AuthErrorBoundary,
} from './unified-error-boundary';

// Query error handling
export {
  createUnifiedQueryErrorHandler,
  unifiedRetryConfig,
  unifiedQueryClientConfig,
  useQueryErrorHandler,
  useMutationErrorHandler,
  shouldUseErrorBoundary,
} from './query-error-handler';

/**
 * Migration helpers for existing code
 * These help transition from old error handling patterns
 */

// Legacy compatibility - maps old error handler to new system
export const settingsErrorHandler = errorHandler;
export const SettingsErrorHandler = UnifiedErrorHandler;

// Legacy error boundary compatibility
export const SettingsErrorBoundary = UnifiedErrorBoundary;
export const AuthErrorBoundaryLegacy = UnifiedErrorBoundary;

// Legacy hook compatibility
export const useSettingsErrorHandler = useUnifiedErrorHandler;

/**
 * Convenience functions for common error scenarios
 */

// Network error helpers
export const handleNetworkError = (error: unknown) => 
  handleError(error, 'network');

export const handleApiError = (error: unknown, endpoint?: string) => 
  handleError(error, `api:${endpoint || 'unknown'}`);

// Authentication error helpers
export const handleAuthError = (error: unknown) => 
  handleError(error, 'authentication');

export const handlePermissionError = (error: unknown) => 
  handleError(error, 'permissions');

// Form error helpers
export const handleFormError = (error: unknown, formName?: string) => 
  handleError(error, `form:${formName || 'unknown'}`);

// Real-time error helpers
export const handleSubscriptionError = (error: unknown, subscription?: string) => 
  handleRealtimeError(error);

// Cache error helpers
export const handleQueryCacheError = (error: unknown, queryKey?: string) => 
  handleCacheError(error);

/**
 * Error reporting utilities
 */
export function reportError(error: UnifiedError, context?: string) {
  if (process.env.NODE_ENV === 'production') {
    // Send to monitoring service
    // Example: Sentry.captureException(error, { extra: { context } });
  }
}

export function reportPerformanceError(error: UnifiedError, metrics?: Record<string, number>) {
  if (process.env.NODE_ENV === 'production') {
    // Send performance-related errors to monitoring
    // Example: analytics.track('performance_error', { error: error.code, metrics });
  }
}

/**
 * Error recovery utilities
 */
export function createErrorRecoveryStrategy(
  error: UnifiedError,
  recoveryActions: {
    retry?: () => void;
    fallback?: () => void;
    redirect?: (path: string) => void;
  }
) {
  if (!error.recoverable) {
    return null;
  }

  switch (error.category) {
    case 'network':
      return recoveryActions.retry || recoveryActions.fallback;
    case 'auth':
      return recoveryActions.redirect;
    case 'validation':
      return recoveryActions.retry;
    default:
      return recoveryActions.fallback || recoveryActions.retry;
  }
}

/**
 * Error context providers for React components
 */
export const ErrorContext = {
  AUTHENTICATION: 'authentication',
  SETTINGS: 'settings',
  TICKETS: 'tickets',
  REAL_TIME: 'real-time',
  FORMS: 'forms',
  API: 'api',
  CACHE: 'cache',
  NAVIGATION: 'navigation',
} as const;

export type ErrorContextType = typeof ErrorContext[keyof typeof ErrorContext];

/**
 * Default export for easy importing
 */
const unifiedErrorManagement = {
  // Core
  errorHandler,
  handleError,
  ERROR_CODES,
  
  // Boundaries
  UnifiedErrorBoundary,
  PageErrorBoundary,
  SectionErrorBoundary,
  ComponentErrorBoundary,
  AuthErrorBoundary,
  
  // Hooks
  useUnifiedErrorHandler,
  useQueryErrorHandler,
  useMutationErrorHandler,
  
  // Query integration
  createUnifiedQueryErrorHandler,
  unifiedQueryClientConfig,
  
  // Utilities
  showSuccess,
  showInfo,
  handleFormValidationError,
  handleCacheError,
  handleRealtimeError,
  
  // Context
  ErrorContext,
};

export default unifiedErrorManagement;
