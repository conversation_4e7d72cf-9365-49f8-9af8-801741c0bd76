/**
 * Unified Error Handling System - 2025 Optimized
 * 
 * Centralized error management consolidating all error handling patterns
 * Eliminates duplicate error logic and provides consistent error handling
 */

import { toast } from 'sonner';

// Unified error types
export interface ValidationErrorDetail {
  field: string;
  message: string;
  code?: string;
}

export interface HttpErrorDetail {
  status: number;
  statusText?: string;
  url?: string;
}

export interface NetworkErrorDetail {
  type: 'fetch' | 'offline' | 'timeout' | 'connection';
  message: string;
}

export interface ClerkErrorDetail {
  code: string;
  message: string;
  longMessage?: string;
}

export type ErrorDetails =
  | ValidationErrorDetail[]
  | HttpErrorDetail
  | NetworkErrorDetail
  | ClerkErrorDetail
  | Error
  | unknown;

export interface UnifiedError {
  code: string;
  message: string;
  details?: ErrorDetails;
  recoverable: boolean;
  category: 'network' | 'auth' | 'validation' | 'permission' | 'server' | 'client' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

// Error categories for consistent handling
export const ERROR_CODES = {
  // Network errors
  NETWORK_OFFLINE: 'NETWORK_OFFLINE',
  NETWORK_ERROR: 'NETWORK_ERROR',
  NETWORK_TIMEOUT: 'NETWORK_TIMEOUT',
  
  // Authentication errors
  UNAUTHORIZED: 'UNAUTHORIZED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  
  // Permission errors
  FORBIDDEN: 'FORBIDDEN',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  FORM_VALIDATION_ERROR: 'FORM_VALIDATION_ERROR',
  
  // Server errors
  SERVER_ERROR: 'SERVER_ERROR',
  RATE_LIMITED: 'RATE_LIMITED',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  
  // Client errors
  NOT_FOUND: 'NOT_FOUND',
  BAD_REQUEST: 'BAD_REQUEST',
  
  // Generic errors
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  REALTIME_ERROR: 'REALTIME_ERROR',
} as const;

export type ErrorCode = typeof ERROR_CODES[keyof typeof ERROR_CODES];

/**
 * Unified Error Handler Class
 * Consolidates all error handling patterns into a single, reusable system
 */
export class UnifiedErrorHandler {
  private static instance: UnifiedErrorHandler;
  
  static getInstance(): UnifiedErrorHandler {
    if (!UnifiedErrorHandler.instance) {
      UnifiedErrorHandler.instance = new UnifiedErrorHandler();
    }
    return UnifiedErrorHandler.instance;
  }

  /**
   * Main error handling method - processes any error type
   */
  handleError(error: unknown, context?: string): UnifiedError {
    const unifiedError = this.processError(error, context);
    
    // Show user notification based on severity and category
    this.showUserNotification(unifiedError);
    
    // Log error for debugging
    this.logError(unifiedError, context);
    
    return unifiedError;
  }

  /**
   * Process error into unified format
   */
  private processError(error: unknown, context?: string): UnifiedError {
    // Network offline
    if (!navigator.onLine) {
      return {
        code: ERROR_CODES.NETWORK_OFFLINE,
        message: 'You are offline. Changes will be saved when connection is restored.',
        category: 'network',
        severity: 'medium',
        recoverable: true,
      };
    }

    // Fetch/Network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        code: ERROR_CODES.NETWORK_ERROR,
        message: 'Network error. Please check your connection and try again.',
        category: 'network',
        severity: 'medium',
        recoverable: true,
        details: { type: 'fetch', message: error.message } as NetworkErrorDetail,
      };
    }

    // HTTP errors
    if (this.isHttpError(error)) {
      return this.handleHttpError(error);
    }

    // Clerk authentication errors
    if (this.isClerkError(error)) {
      return this.handleClerkError(error);
    }

    // Validation errors
    if (this.isValidationError(error)) {
      return {
        code: ERROR_CODES.VALIDATION_ERROR,
        message: 'Please check your input and try again.',
        category: 'validation',
        severity: 'low',
        recoverable: true,
        details: error.errors || error,
      };
    }

    // Generic error fallback
    return {
      code: ERROR_CODES.UNKNOWN_ERROR,
      message: this.isErrorObject(error) 
        ? error.message 
        : 'An unexpected error occurred. Please try again.',
      category: 'unknown',
      severity: 'medium',
      recoverable: true,
      details: error,
    };
  }

  /**
   * Handle HTTP errors with specific status codes
   */
  private handleHttpError(error: { status: number; statusText?: string }): UnifiedError {
    switch (error.status) {
      case 401:
        return {
          code: ERROR_CODES.UNAUTHORIZED,
          message: 'Your session has expired. Please sign in again.',
          category: 'auth',
          severity: 'high',
          recoverable: false,
          details: error as HttpErrorDetail,
        };

      case 403:
        return {
          code: ERROR_CODES.FORBIDDEN,
          message: 'You do not have permission to perform this action.',
          category: 'permission',
          severity: 'medium',
          recoverable: false,
          details: error as HttpErrorDetail,
        };

      case 404:
        return {
          code: ERROR_CODES.NOT_FOUND,
          message: 'The requested resource was not found.',
          category: 'client',
          severity: 'low',
          recoverable: false,
          details: error as HttpErrorDetail,
        };

      case 429:
        return {
          code: ERROR_CODES.RATE_LIMITED,
          message: 'Too many requests. Please wait a moment and try again.',
          category: 'server',
          severity: 'medium',
          recoverable: true,
          details: error as HttpErrorDetail,
        };

      case 500:
        return {
          code: ERROR_CODES.SERVER_ERROR,
          message: 'Server error. Please try again in a few moments.',
          category: 'server',
          severity: 'high',
          recoverable: true,
          details: error as HttpErrorDetail,
        };

      default:
        return {
          code: ERROR_CODES.UNKNOWN_ERROR,
          message: `Request failed with status ${error.status}. Please try again.`,
          category: 'server',
          severity: 'medium',
          recoverable: true,
          details: error as HttpErrorDetail,
        };
    }
  }

  /**
   * Handle Clerk-specific errors
   */
  private handleClerkError(error: { errors?: Array<{ code: string; message?: string }> }): UnifiedError {
    const clerkError = error.errors?.[0];
    
    if (!clerkError) {
      return {
        code: ERROR_CODES.UNKNOWN_ERROR,
        message: 'Authentication error occurred.',
        category: 'auth',
        severity: 'medium',
        recoverable: true,
      };
    }

    switch (clerkError.code) {
      case 'form_password_incorrect':
        return {
          code: ERROR_CODES.INVALID_CREDENTIALS,
          message: 'Current password is incorrect.',
          category: 'auth',
          severity: 'low',
          recoverable: true,
          details: clerkError as ClerkErrorDetail,
        };

      case 'form_password_pwned':
        return {
          code: ERROR_CODES.VALIDATION_ERROR,
          message: 'This password has been found in a data breach. Please choose a different password.',
          category: 'validation',
          severity: 'medium',
          recoverable: true,
          details: clerkError as ClerkErrorDetail,
        };

      default:
        return {
          code: ERROR_CODES.UNKNOWN_ERROR,
          message: clerkError.message || 'Authentication error occurred.',
          category: 'auth',
          severity: 'medium',
          recoverable: true,
          details: clerkError as ClerkErrorDetail,
        };
    }
  }

  /**
   * Show appropriate user notification
   */
  private showUserNotification(error: UnifiedError): void {
    // Don't show notifications for cache or low-severity errors
    if (error.code === ERROR_CODES.CACHE_ERROR || error.severity === 'low') {
      return;
    }

    switch (error.severity) {
      case 'critical':
      case 'high':
        toast.error(error.message);
        break;
      case 'medium':
        toast.error(error.message);
        break;
      default:
        // Low severity errors are handled silently or with minimal UI feedback
        break;
    }
  }

  /**
   * Log error for debugging and monitoring
   */
  private logError(error: UnifiedError, context?: string): void {
    if (process.env.NODE_ENV === 'development') {
      console.error('Unified Error Handler:', {
        code: error.code,
        message: error.message,
        category: error.category,
        severity: error.severity,
        context,
        details: error.details,
      });
    }

    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production' && error.severity === 'critical') {
      // Example: Send to Sentry, LogRocket, etc.
      // this.sendToMonitoring(error, context);
    }
  }

  // Type guards
  private isHttpError(error: unknown): error is { status: number; statusText?: string } {
    return (
      typeof error === 'object' &&
      error !== null &&
      'status' in error &&
      typeof (error as { status: unknown }).status === 'number'
    );
  }

  private isClerkError(error: unknown): error is { errors?: Array<{ code: string; message?: string }> } {
    return (
      typeof error === 'object' &&
      error !== null &&
      'errors' in error &&
      Array.isArray((error as { errors: unknown }).errors)
    );
  }

  private isValidationError(error: unknown): error is { errors: ValidationErrorDetail[] } {
    return (
      typeof error === 'object' &&
      error !== null &&
      'errors' in error &&
      Array.isArray((error as { errors: unknown }).errors) &&
      (error as { errors: unknown[] }).errors.every(
        (e) => typeof e === 'object' && e !== null && 'field' in e && 'message' in e
      )
    );
  }

  private isErrorObject(error: unknown): error is Error {
    return error instanceof Error;
  }

  /**
   * Specialized handlers for specific contexts
   */
  
  // Cache error handler (non-critical)
  handleCacheError(error?: unknown): void {
    // Cache errors are handled gracefully without user notification
    if (process.env.NODE_ENV === 'development') {
      console.warn('Cache error (handled gracefully):', error);
    }
  }

  // Real-time connection error handler
  handleRealtimeError(error?: unknown): void {
    // Real-time errors are handled by fallback mechanisms
    if (process.env.NODE_ENV === 'development') {
      console.warn('Real-time connection error (fallback active):', error);
    }
  }

  // Form validation error handler
  handleFormValidationError(errors: Record<string, { message?: string }>): void {
    const firstError = Object.values(errors)[0];
    const message = firstError?.message || 'Please check your input and try again.';
    
    toast.error(message);
  }

  // Success message handler
  showSuccess(message: string): void {
    toast.success(message);
  }

  // Info message handler
  showInfo(message: string): void {
    toast.info(message);
  }
}

// Export singleton instance
export const errorHandler = UnifiedErrorHandler.getInstance();

// Export convenience functions
export const handleError = (error: unknown, context?: string) => 
  errorHandler.handleError(error, context);

export const handleCacheError = (error?: unknown) => 
  errorHandler.handleCacheError(error);

export const handleRealtimeError = (error?: unknown) => 
  errorHandler.handleRealtimeError(error);

export const handleFormValidationError = (errors: Record<string, { message?: string }>) => 
  errorHandler.handleFormValidationError(errors);

export const showSuccess = (message: string) => 
  errorHandler.showSuccess(message);

export const showInfo = (message: string) => 
  errorHandler.showInfo(message);
