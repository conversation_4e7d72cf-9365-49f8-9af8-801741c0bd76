/**
 * Unified Error Boundary - 2025 Optimized
 * 
 * Consolidates all error boundary patterns into a single, reusable component
 * Replaces AuthErrorBoundary, SettingsErrorBoundary, and other scattered boundaries
 */

'use client';

import React, { Component, ReactNode, useCallback, useEffect, useState } from 'react';
import { errorHandler, UnifiedError } from './unified-error-handler';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  context?: string;
  onError?: (error: UnifiedError, errorInfo: React.ErrorInfo) => void;
  showRetry?: boolean;
  showDetails?: boolean;
  level?: 'page' | 'section' | 'component';
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  unifiedError: UnifiedError | null;
}

/**
 * Unified Error Boundary Component
 * Handles all types of errors with consistent behavior
 */
export class UnifiedErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      unifiedError: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  override componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Process error through unified handler
    const unifiedError = errorHandler.handleError(error, this.props.context);
    
    this.setState({
      error,
      errorInfo,
      unifiedError,
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(unifiedError, errorInfo);
    }

    // Report to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { extra: errorInfo });
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      unifiedError: null,
    });
  };

  override render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Render appropriate error UI based on level and error type
      return this.renderErrorUI();
    }

    return this.props.children;
  }

  private renderErrorUI() {
    const { level = 'component', showRetry = true, showDetails = false } = this.props;
    const { unifiedError, error } = this.state;

    // For critical auth errors, render minimal UI
    if (unifiedError?.category === 'auth' && unifiedError?.severity === 'high') {
      return this.renderAuthErrorUI();
    }

    // For page-level errors, render full error page
    if (level === 'page') {
      return this.renderPageErrorUI();
    }

    // For section-level errors, render contained error UI
    if (level === 'section') {
      return this.renderSectionErrorUI();
    }

    // For component-level errors, render minimal error UI
    return this.renderComponentErrorUI();
  }

  private renderAuthErrorUI() {
    // Minimal UI for auth errors to maintain seamless experience
    if (process.env.NODE_ENV === 'production') {
      return null; // Silent failure in production
    }

    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
        <p className="text-sm text-red-600">Authentication error occurred</p>
        {this.props.showRetry && (
          <button
            onClick={this.handleRetry}
            className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded text-sm hover:bg-red-200"
          >
            Retry
          </button>
        )}
      </div>
    );
  }

  private renderPageErrorUI() {
    const { unifiedError } = this.state;
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
          <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full">
            <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="mt-4 text-center">
            <h1 className="text-lg font-medium text-gray-900">Something went wrong</h1>
            <p className="mt-2 text-sm text-gray-600">
              {unifiedError?.message || 'An unexpected error occurred. Please try again.'}
            </p>
            {this.props.showRetry && (
              <button
                onClick={this.handleRetry}
                className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  private renderSectionErrorUI() {
    const { unifiedError } = this.state;
    
    return (
      <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Section</h3>
            <p className="mt-1 text-sm text-red-700">
              {unifiedError?.message || 'This section could not be loaded.'}
            </p>
            {this.props.showRetry && (
              <button
                onClick={this.handleRetry}
                className="mt-3 bg-red-100 text-red-800 px-3 py-1 rounded text-sm hover:bg-red-200"
              >
                Retry
              </button>
            )}
          </div>
        </div>
      </div>
    );
  }

  private renderComponentErrorUI() {
    const { unifiedError } = this.state;
    
    return (
      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded">
        <div className="flex items-center">
          <svg className="h-4 w-4 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <span className="text-sm text-yellow-800">
            {unifiedError?.recoverable ? 'Temporary issue' : 'Component error'}
          </span>
          {this.props.showRetry && unifiedError?.recoverable && (
            <button
              onClick={this.handleRetry}
              className="ml-2 text-xs text-yellow-600 hover:text-yellow-800 underline"
            >
              retry
            </button>
          )}
        </div>
      </div>
    );
  }
}

/**
 * Hook-based error boundary for functional components
 * Replaces useSettingsErrorHandler and similar patterns
 */
export function useUnifiedErrorHandler(context?: string) {
  const [error, setError] = useState<UnifiedError | null>(null);

  const resetError = useCallback(() => {
    setError(null);
  }, []);

  const handleError = useCallback((error: unknown) => {
    const unifiedError = errorHandler.handleError(error, context);
    setError(unifiedError);
  }, [context]);

  useEffect(() => {
    if (error && process.env.NODE_ENV === 'production') {
      // Report to monitoring service
      // Example: analytics.track('error_occurred', { error: error.code, context });
    }
  }, [error]);

  return {
    error,
    resetError,
    handleError,
    hasError: !!error,
  };
}

/**
 * Higher-order component to wrap components with unified error boundary
 * Replaces withAuthErrorBoundary and similar HOCs
 */
export function withUnifiedErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    fallback?: ReactNode;
    context?: string;
    level?: 'page' | 'section' | 'component';
    showRetry?: boolean;
  } = {}
) {
  return function WrappedComponent(props: P) {
    return (
      <UnifiedErrorBoundary
        fallback={options.fallback}
        context={options.context}
        level={options.level}
        showRetry={options.showRetry}
      >
        <Component {...props} />
      </UnifiedErrorBoundary>
    );
  };
}

/**
 * Specialized error boundaries for common use cases
 */

// Page-level error boundary
export function PageErrorBoundary({ children, context }: { children: ReactNode; context?: string }) {
  return (
    <UnifiedErrorBoundary level="page" context={context} showRetry={true}>
      {children}
    </UnifiedErrorBoundary>
  );
}

// Section-level error boundary
export function SectionErrorBoundary({ children, context }: { children: ReactNode; context?: string }) {
  return (
    <UnifiedErrorBoundary level="section" context={context} showRetry={true}>
      {children}
    </UnifiedErrorBoundary>
  );
}

// Component-level error boundary
export function ComponentErrorBoundary({ children, context }: { children: ReactNode; context?: string }) {
  return (
    <UnifiedErrorBoundary level="component" context={context} showRetry={true}>
      {children}
    </UnifiedErrorBoundary>
  );
}

// Auth-specific error boundary (silent failures)
export function AuthErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <UnifiedErrorBoundary 
      level="component" 
      context="authentication" 
      showRetry={false}
      fallback={process.env.NODE_ENV === 'production' ? null : undefined}
    >
      {children}
    </UnifiedErrorBoundary>
  );
}
