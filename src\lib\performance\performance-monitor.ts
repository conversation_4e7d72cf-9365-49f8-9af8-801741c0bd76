/**
 * Performance Monitoring System - 2025 Optimized
 *
 * Comprehensive performance tracking for React Query, Zustand, and component rendering
 * Provides baseline metrics and optimization insights
 */

interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  category: 'query' | 'render' | 'mutation' | 'subscription' | 'bundle';
  metadata?: Record<string, unknown>;
}

interface ComponentRenderMetric {
  componentName: string;
  renderCount: number;
  totalTime: number;
  averageTime: number;
  lastRender: number;
}

interface QueryPerformanceMetric {
  queryKey: string;
  fetchTime: number;
  cacheHit: boolean;
  dataSize: number;
  timestamp: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private renderMetrics = new Map<string, ComponentRenderMetric>();
  private queryMetrics: QueryPerformanceMetric[] = [];
  private maxMetrics = 1000; // Keep last 1000 metrics
  private isEnabled = process.env.NODE_ENV === 'development';

  /**
   * Record a performance metric
   */
  recordMetric(
    name: string,
    value: number,
    category: PerformanceMetric['category'],
    metadata?: Record<string, unknown>
  ): void {
    if (!this.isEnabled) return;

    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: performance.now(),
      category,
      metadata,
    };

    this.metrics.push(metric);

    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.shift();
    }

    // Log slow operations
    if (value > 100) {
      console.warn(
        `🐌 Slow operation detected: ${name} took ${value.toFixed(2)}ms`,
        metadata
      );
    }
  }

  /**
   * Record component render performance
   */
  recordRender(componentName: string, renderTime: number): void {
    if (!this.isEnabled) return;

    const existing = this.renderMetrics.get(componentName);
    if (existing) {
      existing.renderCount++;
      existing.totalTime += renderTime;
      existing.averageTime = existing.totalTime / existing.renderCount;
      existing.lastRender = performance.now();
    } else {
      this.renderMetrics.set(componentName, {
        componentName,
        renderCount: 1,
        totalTime: renderTime,
        averageTime: renderTime,
        lastRender: performance.now(),
      });
    }

    // Log excessive re-renders
    const metric = this.renderMetrics.get(componentName)!;
    if (metric.renderCount > 10 && metric.averageTime > 16) {
      console.warn(
        `🔄 Excessive re-renders: ${componentName} (${metric.renderCount} renders, avg: ${metric.averageTime.toFixed(2)}ms)`
      );
    }
  }

  /**
   * Record React Query performance
   */
  recordQuery(
    queryKey: string,
    fetchTime: number,
    cacheHit: boolean,
    dataSize: number
  ): void {
    if (!this.isEnabled) return;

    const metric: QueryPerformanceMetric = {
      queryKey,
      fetchTime,
      cacheHit,
      dataSize,
      timestamp: performance.now(),
    };

    this.queryMetrics.push(metric);

    // Keep only recent query metrics
    if (this.queryMetrics.length > this.maxMetrics) {
      this.queryMetrics.shift();
    }

    // Log slow queries
    if (fetchTime > 1000) {
      console.warn(
        `🐌 Slow query: ${queryKey} took ${fetchTime.toFixed(2)}ms (${cacheHit ? 'cache hit' : 'network'})`
      );
    }
  }

  /**
   * Get performance summary
   */
  getSummary(): {
    totalMetrics: number;
    averageQueryTime: number;
    cacheHitRate: number;
    slowestComponents: ComponentRenderMetric[];
    slowestQueries: QueryPerformanceMetric[];
    bundleSize?: number;
  } {
    const queryTimes = this.queryMetrics.map((m) => m.fetchTime);
    const cacheHits = this.queryMetrics.filter((m) => m.cacheHit).length;

    const slowestComponents = Array.from(this.renderMetrics.values())
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 5);

    const slowestQueries = this.queryMetrics
      .sort((a, b) => b.fetchTime - a.fetchTime)
      .slice(0, 5);

    return {
      totalMetrics: this.metrics.length,
      averageQueryTime:
        queryTimes.length > 0
          ? queryTimes.reduce((a, b) => a + b, 0) / queryTimes.length
          : 0,
      cacheHitRate:
        this.queryMetrics.length > 0
          ? (cacheHits / this.queryMetrics.length) * 100
          : 0,
      slowestComponents,
      slowestQueries,
    };
  }

  /**
   * Get metrics by category
   */
  getMetricsByCategory(
    category: PerformanceMetric['category']
  ): PerformanceMetric[] {
    return this.metrics.filter((m) => m.category === category);
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics = [];
    this.renderMetrics.clear();
    this.queryMetrics = [];
  }

  /**
   * Export metrics for analysis
   */
  exportMetrics(): {
    metrics: PerformanceMetric[];
    renderMetrics: ComponentRenderMetric[];
    queryMetrics: QueryPerformanceMetric[];
    summary: ReturnType<typeof this.getSummary>;
  } {
    return {
      metrics: [...this.metrics],
      renderMetrics: Array.from(this.renderMetrics.values()),
      queryMetrics: [...this.queryMetrics],
      summary: this.getSummary(),
    };
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

/**
 * React hook for measuring component render performance
 */
export function useRenderPerformance(componentName: string) {
  if (process.env.NODE_ENV !== 'development') return;

  const startTime = performance.now();

  // Use useLayoutEffect for more accurate timing
  if (typeof window !== 'undefined') {
    const { useLayoutEffect } = require('react');
    useLayoutEffect(() => {
      const renderTime = performance.now() - startTime;
      performanceMonitor.recordRender(componentName, renderTime);
    });
  }
}

/**
 * Higher-order component for automatic render performance tracking
 */
export function withPerformanceTracking<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  if (typeof window === 'undefined') return Component;

  const React = require('react');

  const WrappedComponent = React.memo((props: P) => {
    const name =
      componentName || Component.displayName || Component.name || 'Unknown';
    useRenderPerformance(name);
    return React.createElement(Component, props);
  });

  WrappedComponent.displayName = `withPerformanceTracking(${componentName || Component.displayName || Component.name})`;
  return WrappedComponent;
}

/**
 * Utility for measuring async operations
 */
export async function measureAsync<T>(
  operation: () => Promise<T>,
  name: string,
  category: PerformanceMetric['category'] = 'query'
): Promise<T> {
  const startTime = performance.now();

  try {
    const result = await operation();
    const duration = performance.now() - startTime;
    performanceMonitor.recordMetric(name, duration, category);
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    performanceMonitor.recordMetric(`${name}_error`, duration, category, {
      error: String(error),
    });
    throw error;
  }
}

/**
 * Bundle size analyzer
 */
export function analyzeBundleSize(): void {
  if (typeof window === 'undefined') return;

  // Estimate bundle size from loaded scripts
  const scripts = Array.from(document.querySelectorAll('script[src]'));
  let totalSize = 0;

  scripts.forEach((script) => {
    const src = script.getAttribute('src');
    if (src && !src.startsWith('http')) {
      // Estimate size based on script length (rough approximation)
      totalSize += src.length * 100; // Very rough estimate
    }
  });

  performanceMonitor.recordMetric('bundle_size_estimate', totalSize, 'bundle');
}

// Auto-analyze bundle size on load
if (typeof window !== 'undefined') {
  window.addEventListener('load', analyzeBundleSize);
}
