/**
 * Baseline Performance Analysis - 2025 Optimized
 * 
 * Comprehensive analysis of current system performance
 * Identifies optimization opportunities and establishes benchmarks
 */

import { performanceMonitor } from './performance-monitor';

interface BaselineMetrics {
  timestamp: number;
  reactQuery: {
    totalQueries: number;
    averageFetchTime: number;
    cacheHitRate: number;
    slowQueries: number;
    errorRate: number;
  };
  zustand: {
    totalStores: number;
    averageUpdateTime: number;
    subscriptionCount: number;
    memoryUsage: number;
  };
  components: {
    totalComponents: number;
    averageRenderTime: number;
    slowComponents: number;
    reRenderRate: number;
  };
  realtime: {
    subscriptionCount: number;
    averageLatency: number;
    messageRate: number;
    errorRate: number;
  };
  bundle: {
    estimatedSize: number;
    loadTime: number;
    chunkCount: number;
  };
}

interface OptimizationOpportunity {
  category: 'query' | 'render' | 'bundle' | 'realtime' | 'state';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  impact: string;
  recommendation: string;
  estimatedImprovement: string;
}

class BaselineAnalyzer {
  private startTime = performance.now();

  /**
   * Analyze current React Query performance
   */
  analyzeReactQuery(): BaselineMetrics['reactQuery'] {
    const summary = performanceMonitor.getSummary();
    const queryMetrics = performanceMonitor.getMetricsByCategory('query');
    
    const slowQueries = queryMetrics.filter(m => m.value > 1000).length;
    const errorQueries = queryMetrics.filter(m => m.name.includes('error')).length;
    const errorRate = queryMetrics.length > 0 ? (errorQueries / queryMetrics.length) * 100 : 0;

    return {
      totalQueries: queryMetrics.length,
      averageFetchTime: summary.averageQueryTime,
      cacheHitRate: summary.cacheHitRate,
      slowQueries,
      errorRate,
    };
  }

  /**
   * Analyze Zustand store performance
   */
  analyzeZustand(): BaselineMetrics['zustand'] {
    // Estimate Zustand performance based on available metrics
    const stateMetrics = performanceMonitor.getMetricsByCategory('render');
    const storeUpdates = stateMetrics.filter(m => m.name.includes('store') || m.name.includes('state'));
    
    const averageUpdateTime = storeUpdates.length > 0 
      ? storeUpdates.reduce((sum, m) => sum + m.value, 0) / storeUpdates.length 
      : 0;

    return {
      totalStores: 3, // Estimated based on codebase analysis
      averageUpdateTime,
      subscriptionCount: 0, // Would need runtime analysis
      memoryUsage: 0, // Would need runtime analysis
    };
  }

  /**
   * Analyze component render performance
   */
  analyzeComponents(): BaselineMetrics['components'] {
    const summary = performanceMonitor.getSummary();
    const slowComponents = summary.slowestComponents.filter(c => c.averageTime > 16).length;
    const totalRenders = summary.slowestComponents.reduce((sum, c) => sum + c.renderCount, 0);
    const averageRenderTime = summary.slowestComponents.length > 0
      ? summary.slowestComponents.reduce((sum, c) => sum + c.averageTime, 0) / summary.slowestComponents.length
      : 0;

    return {
      totalComponents: summary.slowestComponents.length,
      averageRenderTime,
      slowComponents,
      reRenderRate: totalRenders,
    };
  }

  /**
   * Analyze real-time performance
   */
  analyzeRealtime(): BaselineMetrics['realtime'] {
    const subscriptionMetrics = performanceMonitor.getMetricsByCategory('subscription');
    const averageLatency = subscriptionMetrics.length > 0
      ? subscriptionMetrics.reduce((sum, m) => sum + m.value, 0) / subscriptionMetrics.length
      : 0;

    return {
      subscriptionCount: 0, // Would need runtime analysis
      averageLatency,
      messageRate: 0, // Would need runtime analysis
      errorRate: 0, // Would need runtime analysis
    };
  }

  /**
   * Analyze bundle performance
   */
  analyzeBundle(): BaselineMetrics['bundle'] {
    const bundleMetrics = performanceMonitor.getMetricsByCategory('bundle');
    const estimatedSize = bundleMetrics.find(m => m.name === 'bundle_size_estimate')?.value || 0;
    const loadTime = performance.now() - this.startTime;

    return {
      estimatedSize,
      loadTime,
      chunkCount: 0, // Would need build analysis
    };
  }

  /**
   * Generate complete baseline metrics
   */
  generateBaseline(): BaselineMetrics {
    return {
      timestamp: Date.now(),
      reactQuery: this.analyzeReactQuery(),
      zustand: this.analyzeZustand(),
      components: this.analyzeComponents(),
      realtime: this.analyzeRealtime(),
      bundle: this.analyzeBundle(),
    };
  }

  /**
   * Identify optimization opportunities
   */
  identifyOptimizations(baseline: BaselineMetrics): OptimizationOpportunity[] {
    const opportunities: OptimizationOpportunity[] = [];

    // React Query optimizations
    if (baseline.reactQuery.averageFetchTime > 500) {
      opportunities.push({
        category: 'query',
        severity: 'high',
        description: 'Slow React Query fetch times detected',
        impact: `Average fetch time: ${baseline.reactQuery.averageFetchTime.toFixed(2)}ms`,
        recommendation: 'Implement select functions, optimize query keys, and enhance caching strategies',
        estimatedImprovement: '40-60% faster query performance',
      });
    }

    if (baseline.reactQuery.cacheHitRate < 70) {
      opportunities.push({
        category: 'query',
        severity: 'medium',
        description: 'Low cache hit rate',
        impact: `Cache hit rate: ${baseline.reactQuery.cacheHitRate.toFixed(1)}%`,
        recommendation: 'Optimize staleTime and gcTime configurations, implement better cache invalidation',
        estimatedImprovement: '20-30% reduction in network requests',
      });
    }

    // Component render optimizations
    if (baseline.components.averageRenderTime > 16) {
      opportunities.push({
        category: 'render',
        severity: 'high',
        description: 'Slow component render times detected',
        impact: `Average render time: ${baseline.components.averageRenderTime.toFixed(2)}ms`,
        recommendation: 'Implement React.memo, useMemo, and useCallback optimizations',
        estimatedImprovement: '50-70% faster rendering',
      });
    }

    if (baseline.components.slowComponents > 5) {
      opportunities.push({
        category: 'render',
        severity: 'medium',
        description: 'Multiple slow components identified',
        impact: `${baseline.components.slowComponents} components rendering slowly`,
        recommendation: 'Apply performance tracking and optimize heavy components',
        estimatedImprovement: '30-50% overall performance improvement',
      });
    }

    // Bundle optimizations
    if (baseline.bundle.estimatedSize > 1000000) { // 1MB
      opportunities.push({
        category: 'bundle',
        severity: 'medium',
        description: 'Large bundle size detected',
        impact: `Estimated bundle size: ${(baseline.bundle.estimatedSize / 1024 / 1024).toFixed(2)}MB`,
        recommendation: 'Implement code splitting, tree shaking, and dynamic imports',
        estimatedImprovement: '20-40% smaller bundle size',
      });
    }

    // Zustand optimizations
    if (baseline.zustand.averageUpdateTime > 10) {
      opportunities.push({
        category: 'state',
        severity: 'medium',
        description: 'Slow Zustand state updates',
        impact: `Average update time: ${baseline.zustand.averageUpdateTime.toFixed(2)}ms`,
        recommendation: 'Implement useShallow selectors and optimize store structure',
        estimatedImprovement: '30-50% faster state updates',
      });
    }

    return opportunities.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  /**
   * Generate optimization report
   */
  generateReport(): {
    baseline: BaselineMetrics;
    opportunities: OptimizationOpportunity[];
    summary: {
      totalIssues: number;
      criticalIssues: number;
      estimatedImprovementPotential: string;
    };
  } {
    const baseline = this.generateBaseline();
    const opportunities = this.identifyOptimizations(baseline);
    
    const criticalIssues = opportunities.filter(o => o.severity === 'critical').length;
    const highIssues = opportunities.filter(o => o.severity === 'high').length;
    
    let improvementPotential = 'Low';
    if (criticalIssues > 0) improvementPotential = 'Very High';
    else if (highIssues > 2) improvementPotential = 'High';
    else if (opportunities.length > 3) improvementPotential = 'Medium';

    return {
      baseline,
      opportunities,
      summary: {
        totalIssues: opportunities.length,
        criticalIssues,
        estimatedImprovementPotential: improvementPotential,
      },
    };
  }
}

// Export singleton instance
export const baselineAnalyzer = new BaselineAnalyzer();

/**
 * Quick performance check utility
 */
export function quickPerformanceCheck(): {
  status: 'good' | 'warning' | 'critical';
  issues: string[];
  recommendations: string[];
} {
  const report = baselineAnalyzer.generateReport();
  const { baseline, opportunities } = report;
  
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  let status: 'good' | 'warning' | 'critical' = 'good';
  
  if (baseline.reactQuery.averageFetchTime > 1000) {
    issues.push('Very slow query performance detected');
    recommendations.push('Optimize React Query configuration and caching');
    status = 'critical';
  } else if (baseline.reactQuery.averageFetchTime > 500) {
    issues.push('Slow query performance detected');
    recommendations.push('Implement query optimizations');
    status = 'warning';
  }
  
  if (baseline.components.averageRenderTime > 32) {
    issues.push('Very slow component rendering detected');
    recommendations.push('Implement React performance optimizations');
    status = 'critical';
  } else if (baseline.components.averageRenderTime > 16) {
    issues.push('Slow component rendering detected');
    recommendations.push('Apply memoization and render optimizations');
    if (status !== 'critical') status = 'warning';
  }
  
  if (opportunities.filter(o => o.severity === 'critical').length > 0) {
    status = 'critical';
  } else if (opportunities.filter(o => o.severity === 'high').length > 1) {
    if (status !== 'critical') status = 'warning';
  }
  
  return { status, issues, recommendations };
}
