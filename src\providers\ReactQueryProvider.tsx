'use client';

import { QueryClient } from '@tanstack/react-query';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister';
import { useState } from 'react';
import { ModernMessageCache } from '@/lib/cache/modern-dexie-cache';
import {
  createUnifiedQueryErrorHandler,
  unifiedQueryClientConfig,
} from '@/lib/error-management';

// IndexedDB persister for optimal 2025 pattern
const createPersister = () => {
  return createSyncStoragePersister({
    storage: {
      getItem: (key: string) => {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : null;
      },
      setItem: (key: string, value: unknown) => {
        localStorage.setItem(key, JSON.stringify(value));
      },
      removeItem: (key: string) => {
        localStorage.removeItem(key);
      },
    },
    key: 'TicketingApp_ReactQueryCache',
    throttleTime: 1000,
  });
};

// Cleanup old databases
const cleanupOldDatabases = async () => {
  try {
    const databases = await indexedDB.databases();
    for (const db of databases) {
      if (db.name === 'TicketingCacheDB') {
        indexedDB.deleteDatabase(db.name);
        // Old database removed successfully
      }
    }
  } catch (_error) {
    // Failed to cleanup old databases - non-critical error
  }
};

// Global cache cleanup utility for logout only
export const clearAllCaches = async () => {
  try {
    localStorage.removeItem('TicketingApp_ReactQueryCache');
    await ModernMessageCache.cleanupExpiredCache();
    // All caches cleared successfully on logout
  } catch (_error) {
    // Failed to clear caches - non-critical error
  }
};

// Optimal 2025 React Query Provider with Unified Error Handling
function ReactQueryProviderInner({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(() => {
    // Create unified error handlers
    const { queryCache, mutationCache } = createUnifiedQueryErrorHandler();

    return new QueryClient({
      queryCache,
      mutationCache,
      ...unifiedQueryClientConfig,
    });
  });

  const persister = createPersister();

  // Cleanup old databases on mount
  if (typeof window !== 'undefined') {
    cleanupOldDatabases();
  }

  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{
        persister,
        maxAge: 30 * 60 * 1000, // 30 minutes as per requirements
        dehydrateOptions: {
          shouldDehydrateQuery: (query) => query.state.status === 'success',
        },
      }}
    >
      {children}
    </PersistQueryClientProvider>
  );
}

// Main component that gets tenant context
export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return <ReactQueryProviderInner>{children}</ReactQueryProviderInner>;
}
