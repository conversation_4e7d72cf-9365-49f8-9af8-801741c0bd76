/**
 * Unified Query Error Handler - 2025 Optimized
 *
 * Consolidates TanStack Query error handling patterns
 * Provides consistent error handling for queries and mutations
 */

import {
  QueryCache,
  MutationCache,
  Query,
  Mutation,
} from '@tanstack/react-query';
import { errorHandler, ERROR_CODES } from './unified-error-handler';

/**
 * Enhanced error handling for TanStack Query
 */
export function createUnifiedQueryErrorHandler() {
  return {
    queryCache: new QueryCache({
      onError: (error, query) => {
        handleQueryError(error, query);
      },
    }),

    mutationCache: new MutationCache({
      onError: (error, _variables, _context, mutation) => {
        handleMutationError(error, mutation);
      },
    }),
  };
}

/**
 * Handle query errors with unified error system
 */
function handleQueryError(
  error: Error & { status?: number; code?: number },
  query: Query
) {
  const context = `query:${query.queryKey.join(':')}`;

  // Process error through unified handler
  const unifiedError = errorHandler.handleError(error, context);

  // Log query-specific information in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Query Error Details:', {
      queryKey: query.queryKey,
      queryHash: query.queryHash,
      state: query.state,
      error: unifiedError,
    });
  }

  // Handle specific query error scenarios
  if (unifiedError.code === ERROR_CODES.UNAUTHORIZED) {
    // Auth errors - potentially redirect or refresh token
    handleAuthError(query);
  } else if (unifiedError.code === ERROR_CODES.FORBIDDEN) {
    // Permission errors - might need to update UI state
    handlePermissionError(query);
  } else if (unifiedError.code === ERROR_CODES.NOT_FOUND) {
    // Not found errors - might need to redirect or show empty state
    handleNotFoundError(query);
  }
}

/**
 * Handle mutation errors with unified error system
 */
function handleMutationError(
  error: Error & { status?: number; code?: number },
  mutation: Mutation
) {
  const context = `mutation:${mutation.options.mutationKey?.join(':') || 'unknown'}`;

  // Process error through unified handler
  const unifiedError = errorHandler.handleError(error, context);

  // Log mutation-specific information in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Mutation Error Details:', {
      mutationKey: mutation.options.mutationKey,
      mutationId: mutation.mutationId,
      state: mutation.state,
      error: unifiedError,
    });
  }

  // Handle specific mutation error scenarios
  if (unifiedError.code === ERROR_CODES.VALIDATION_ERROR) {
    // Validation errors - might need to highlight form fields
    handleValidationError(mutation, unifiedError);
  } else if (unifiedError.code === ERROR_CODES.RATE_LIMITED) {
    // Rate limiting - might need to implement backoff
    handleRateLimitError(mutation);
  }
}

/**
 * Specialized error handlers for specific scenarios
 */

function handleAuthError(query: Query) {
  // Mark auth-related queries as stale to trigger re-authentication
  if (
    query.queryKey.some(
      (key) =>
        typeof key === 'string' &&
        (key.includes('user') ||
          key.includes('auth') ||
          key.includes('profile'))
    )
  ) {
    query.invalidate();
  }
}

function handlePermissionError(query: Query) {
  // For permission errors, we might want to disable certain UI elements
  // This could be handled through a global state update
  if (process.env.NODE_ENV === 'development') {
    console.warn('Permission denied for query:', query.queryKey);
  }
}

function handleNotFoundError(query: Query) {
  // For not found errors, we might want to redirect or show empty state
  if (process.env.NODE_ENV === 'development') {
    console.warn('Resource not found for query:', query.queryKey);
  }
}

function handleValidationError(mutation: Mutation, unifiedError: any) {
  // Validation errors might need special handling for forms
  if (process.env.NODE_ENV === 'development') {
    console.warn('Validation error for mutation:', {
      mutationKey: mutation.options.mutationKey,
      details: unifiedError.details,
    });
  }
}

function handleRateLimitError(mutation: Mutation) {
  // Rate limit errors might need exponential backoff
  if (process.env.NODE_ENV === 'development') {
    console.warn('Rate limited for mutation:', mutation.options.mutationKey);
  }
}

/**
 * Retry logic configuration for queries and mutations
 */
export const unifiedRetryConfig = {
  queries: {
    retry: (
      failureCount: number,
      error: Error & { status?: number; code?: number }
    ) => {
      // Don't retry auth errors
      if (error?.status === 401 || error?.code === 401) {
        return false;
      }

      // Don't retry client errors (4xx) except for specific cases
      if (error?.status && error.status >= 400 && error.status < 500) {
        // Retry 408 (timeout), 429 (rate limit)
        if (error.status === 408 || error.status === 429) {
          return failureCount < 2;
        }
        return false;
      }

      // Retry server errors (5xx) and network errors
      return failureCount < 2;
    },

    retryDelay: (attemptIndex: number, error: Error & { status?: number }) => {
      // Longer delay for rate limiting
      if (error?.status === 429) {
        return Math.min(2000 * 2 ** attemptIndex, 30000);
      }

      // Standard exponential backoff
      return Math.min(500 * 2 ** attemptIndex, 5000);
    },
  },

  mutations: {
    retry: (
      failureCount: number,
      error: Error & { status?: number; code?: number }
    ) => {
      // Don't retry auth errors
      if (error?.status === 401 || error?.code === 401) {
        return false;
      }

      // Don't retry client errors except for specific cases
      if (error?.status && error.status >= 400 && error.status < 500) {
        // Retry 408 (timeout), 429 (rate limit)
        if (error.status === 408 || error.status === 429) {
          return failureCount < 1;
        }
        return false;
      }

      // Retry server errors once
      return failureCount < 1;
    },

    retryDelay: (attemptIndex: number, error: Error & { status?: number }) => {
      // Longer delay for rate limiting
      if (error?.status === 429) {
        return Math.min(2000 * 2 ** attemptIndex, 30000);
      }

      // Standard exponential backoff
      return Math.min(500 * 2 ** attemptIndex, 5000);
    },
  },
};

/**
 * Default query client configuration with unified error handling
 */
export const unifiedQueryClientConfig = {
  defaultOptions: {
    queries: {
      // Enhanced caching strategy
      staleTime: 30 * 60 * 1000, // 30 minutes
      gcTime: 60 * 60 * 1000, // 1 hour

      // Unified retry configuration
      retry: unifiedRetryConfig.queries.retry,
      retryDelay: unifiedRetryConfig.queries.retryDelay,

      // React 19 optimizations
      notifyOnChangeProps: 'all' as const,
      structuralSharing: true,
      throwOnError: false,

      // Enhanced refetch behavior
      refetchOnWindowFocus: true,
      refetchOnMount: true,
      refetchOnReconnect: true,
      refetchInterval: false as const,
      refetchIntervalInBackground: false,

      // Network mode optimization
      networkMode: 'online' as const,

      // Enhanced error handling
      useErrorBoundary: false,
    },
    mutations: {
      // Unified retry configuration
      retry: unifiedRetryConfig.mutations.retry,
      retryDelay: unifiedRetryConfig.mutations.retryDelay,

      // Enhanced mutation options
      networkMode: 'online' as const,
      throwOnError: false,
      useErrorBoundary: false,
      gcTime: 5 * 60 * 1000, // 5 minutes for mutations
    },
  },
};

/**
 * Utility functions for error handling in hooks
 */

// Handle errors in custom hooks
export function useQueryErrorHandler(context?: string) {
  return (error: unknown) => {
    return errorHandler.handleError(error, context);
  };
}

// Handle mutation errors with optimistic updates rollback
export function useMutationErrorHandler(context?: string) {
  return (error: unknown, variables?: unknown, rollback?: () => void) => {
    const unifiedError = errorHandler.handleError(error, context);

    // Rollback optimistic updates if provided
    if (rollback && unifiedError.recoverable) {
      rollback();
    }

    return unifiedError;
  };
}

/**
 * Error boundary integration for query components
 */
export function shouldUseErrorBoundary(
  error: Error & { status?: number; code?: number }
): boolean {
  // Use error boundary for critical errors that should break the component tree
  if (error?.status === 500 || error?.code === 500) {
    return true;
  }

  // Use error boundary for network errors that can't be recovered
  if (!navigator.onLine && error instanceof TypeError) {
    return false; // Handle offline gracefully
  }

  return false; // Most errors should be handled gracefully
}
