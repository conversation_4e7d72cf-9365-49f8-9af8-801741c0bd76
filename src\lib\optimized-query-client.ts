/**
 * Optimized Query Client Factory - 2025 Best Practices
 *
 * Enhanced QueryClient with advanced caching, error handling, and performance optimizations
 * Integrates React 19 patterns and TanStack Query v5 features
 */

import {
  QueryClient,
  QueryCache,
  MutationCache,
  QueryClientConfig,
} from '@tanstack/react-query';

/**
 * Enhanced error handling for queries and mutations
 */
function handleQueryError(
  error: Error & { status?: number; code?: number },
  query: any
) {
  // Log error for debugging in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Query Error:', {
      queryKey: query.queryKey,
      error: error.message,
      status: error.status || error.code,
    });
  }

  // Handle specific error types
  if (error.status === 401 || error.code === 401) {
    // Auth errors - don't retry and potentially redirect
    console.warn('Authentication error detected');
  } else if (error.status === 403 || error.code === 403) {
    // Permission errors
    console.warn('Permission denied');
  } else if (error.status === 404 || error.code === 404) {
    // Not found errors
    console.warn('Resource not found');
  }
}

function handleMutationError(
  error: Error & { status?: number; code?: number },
  mutation: any
) {
  // Log mutation error for debugging
  if (process.env.NODE_ENV === 'development') {
    console.error('Mutation Error:', {
      mutationKey: mutation.options.mutationKey,
      error: error.message,
      status: error.status || error.code,
    });
  }
}

/**
 * Create optimized QueryClient with 2025 best practices
 */
export function createOptimizedQueryClient(
  config?: Partial<QueryClientConfig>
): QueryClient {
  const queryCache = new QueryCache({
    onError: handleQueryError,
    onSuccess: (data, query) => {
      // Optional: Log successful queries in development
      if (
        process.env.NODE_ENV === 'development' &&
        query.queryKey[0] !== 'user'
      ) {
        console.log('Query Success:', query.queryKey);
      }
    },
  });

  const mutationCache = new MutationCache({
    onError: handleMutationError,
    onSuccess: (data, variables, context, mutation) => {
      // Optional: Log successful mutations in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Mutation Success:', mutation.options.mutationKey);
      }
    },
  });

  return new QueryClient({
    queryCache,
    mutationCache,
    defaultOptions: {
      queries: {
        // Enhanced caching strategy
        staleTime: 30 * 60 * 1000, // 30 minutes
        gcTime: 60 * 60 * 1000, // 1 hour

        // Optimized retry logic
        retry: (
          failureCount,
          error: Error & { status?: number; code?: number }
        ) => {
          // Don't retry auth errors
          if (error?.status === 401 || error?.code === 401) {
            return false;
          }

          // Don't retry client errors (4xx) except for specific cases
          if (error?.status && error.status >= 400 && error.status < 500) {
            // Retry 408 (timeout), 429 (rate limit)
            if (error.status === 408 || error.status === 429) {
              return failureCount < 2;
            }
            return false;
          }

          // Retry server errors (5xx) and network errors
          return failureCount < 2;
        },

        // Enhanced retry delay with exponential backoff
        retryDelay: (attemptIndex, error: Error & { status?: number }) => {
          // Longer delay for rate limiting
          if (error?.status === 429) {
            return Math.min(2000 * 2 ** attemptIndex, 30000);
          }

          // Standard exponential backoff
          return Math.min(500 * 2 ** attemptIndex, 5000);
        },

        // React 19 optimizations
        notifyOnChangeProps: 'all',
        structuralSharing: true,
        throwOnError: false,

        // Enhanced refetch behavior
        refetchOnWindowFocus: true,
        refetchOnMount: true,
        refetchOnReconnect: true,
        refetchInterval: false,
        refetchIntervalInBackground: false,

        // Network mode optimization
        networkMode: 'online',

        // Enhanced error handling
        useErrorBoundary: false,
      },
      mutations: {
        // Optimized mutation retry logic
        retry: (
          failureCount,
          error: Error & { status?: number; code?: number }
        ) => {
          // Don't retry auth errors
          if (error?.status === 401 || error?.code === 401) {
            return false;
          }

          // Don't retry client errors except for specific cases
          if (error?.status && error.status >= 400 && error.status < 500) {
            // Retry 408 (timeout), 429 (rate limit)
            if (error.status === 408 || error.status === 429) {
              return failureCount < 1;
            }
            return false;
          }

          // Retry server errors once
          return failureCount < 1;
        },

        // Enhanced mutation options
        networkMode: 'online',
        throwOnError: false,
        useErrorBoundary: false,
        gcTime: 5 * 60 * 1000, // 5 minutes for mutations
      },
    },
    ...config,
  });
}

/**
 * Query client utilities for advanced operations
 */
export const queryClientUtils = {
  /**
   * Prefetch multiple queries in parallel
   */
  prefetchQueries: async (
    queryClient: QueryClient,
    queries: Array<{
      queryKey: string[];
      queryFn: () => Promise<any>;
      staleTime?: number;
    }>
  ) => {
    const prefetchPromises = queries.map(({ queryKey, queryFn, staleTime }) =>
      queryClient.prefetchQuery({
        queryKey,
        queryFn,
        staleTime: staleTime || 30 * 60 * 1000,
      })
    );

    await Promise.allSettled(prefetchPromises);
  },

  /**
   * Batch invalidate multiple query patterns
   */
  batchInvalidate: (
    queryClient: QueryClient,
    patterns: Array<{ queryKey: string[]; exact?: boolean }>
  ) => {
    patterns.forEach(({ queryKey, exact = false }) => {
      queryClient.invalidateQueries({
        queryKey,
        exact,
      });
    });
  },

  /**
   * Clear stale queries to free memory
   */
  clearStaleQueries: (queryClient: QueryClient) => {
    const queries = queryClient.getQueryCache().getAll();
    const staleQueries = queries.filter((query) => query.isStale());

    staleQueries.forEach((query) => {
      if (query.getObserversCount() === 0) {
        queryClient.removeQueries({ queryKey: query.queryKey });
      }
    });

    return staleQueries.length;
  },

  /**
   * Get cache statistics
   */
  getCacheStats: (queryClient: QueryClient) => {
    const queries = queryClient.getQueryCache().getAll();
    const mutations = queryClient.getMutationCache().getAll();

    const activeQueries = queries.filter((q) => q.getObserversCount() > 0);
    const staleQueries = queries.filter((q) => q.isStale());
    const errorQueries = queries.filter((q) => q.state.status === 'error');

    return {
      totalQueries: queries.length,
      activeQueries: activeQueries.length,
      staleQueries: staleQueries.length,
      errorQueries: errorQueries.length,
      totalMutations: mutations.length,
      activeMutations: mutations.filter((m) => m.state.status === 'pending')
        .length,
    };
  },

  /**
   * Optimize cache by removing unused queries
   */
  optimizeCache: (queryClient: QueryClient) => {
    const queries = queryClient.getQueryCache().getAll();
    let removedCount = 0;

    queries.forEach((query) => {
      // Remove queries with no observers that are stale
      if (query.getObserversCount() === 0 && query.isStale()) {
        queryClient.removeQueries({ queryKey: query.queryKey });
        removedCount++;
      }
    });

    return removedCount;
  },

  /**
   * Warm cache with initial data
   */
  warmCache: (
    queryClient: QueryClient,
    data: Array<{
      queryKey: string[];
      data: any;
      staleTime?: number;
    }>
  ) => {
    data.forEach(({ queryKey, data: initialData, staleTime }) => {
      queryClient.setQueryData(queryKey, initialData);

      if (staleTime) {
        queryClient.setQueryDefaults(queryKey, { staleTime });
      }
    });
  },
};

/**
 * Development-only query debugging utilities
 */
export const queryDebugUtils =
  process.env.NODE_ENV === 'development'
    ? {
        /**
         * Log all active queries
         */
        logActiveQueries: (queryClient: QueryClient) => {
          const queries = queryClient.getQueryCache().getAll();
          const activeQueries = queries.filter(
            (q) => q.getObserversCount() > 0
          );

          console.group('🔍 Active Queries');
          activeQueries.forEach((query) => {
            console.log({
              queryKey: query.queryKey,
              status: query.state.status,
              observers: query.getObserversCount(),
              isStale: query.isStale(),
              dataUpdatedAt: new Date(query.state.dataUpdatedAt),
            });
          });
          console.groupEnd();
        },

        /**
         * Monitor query performance
         */
        monitorQueryPerformance: (queryClient: QueryClient) => {
          const originalFetch = queryClient.getQueryCache().build;

          queryClient.getQueryCache().build = function (...args) {
            const query = originalFetch.apply(this, args);
            const originalFetch2 = query.fetch;

            query.fetch = function (...fetchArgs) {
              const start = performance.now();
              const result = originalFetch2.apply(this, fetchArgs);

              if (result instanceof Promise) {
                result.finally(() => {
                  const duration = performance.now() - start;
                  if (duration > 1000) {
                    console.warn(
                      `🐌 Slow query (${duration.toFixed(2)}ms):`,
                      query.queryKey
                    );
                  }
                });
              }

              return result;
            };

            return query;
          };
        },
      }
    : {};
