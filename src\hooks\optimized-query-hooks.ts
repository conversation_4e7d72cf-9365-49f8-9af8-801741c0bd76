/**
 * Optimized Query Hooks - 2025 Best Practices
 *
 * Enhanced React Query hooks with advanced selectors, memoization, and performance optimizations
 * Implements React 19 patterns and TanStack Query v5 best practices
 */

import {
  useQuery,
  useQueryClient,
  UseQueryOptions,
} from '@tanstack/react-query';
import { useCallback, useMemo } from 'react';
import {
  ticketQueryOptions,
  userQueryOptions,
  tenantQueryOptions,
} from '@/lib/query-options';
import type {
  Ticket,
  User,
  Tenant,
  RoleBasedFilterContext,
  TicketFilterOptions,
} from '@/types';

/**
 * Optimized ticket list hook with advanced selectors
 */
export function useOptimizedTicketList(
  context: RoleBasedFilterContext,
  options?: TicketFilterOptions & {
    select?: (data: Ticket[]) => Ticket[];
    enabled?: boolean;
  }
) {
  // Memoize query options to prevent unnecessary re-renders
  const queryOptions = useMemo(
    () => ticketQueryOptions.list(context, options),
    [context.tenantId, context.role, options?.roleFilter, options?.status]
  );

  // Memoize selector function for stable reference
  const selector = useCallback(
    (data: Ticket[]) => {
      if (options?.select) {
        return options.select(data);
      }
      return data;
    },
    [options?.select]
  );

  return useQuery({
    ...queryOptions,
    select: selector,
    enabled: options?.enabled !== false,
  });
}

/**
 * Optimized ticket detail hook with enhanced caching
 */
export function useOptimizedTicketDetail(
  tenantId: string,
  ticketId: string,
  options?: {
    select?: <T>(data: Ticket) => T;
    enabled?: boolean;
    placeholderData?: Ticket;
  }
) {
  const queryClient = useQueryClient();

  // Memoize query options
  const queryOptions = useMemo(
    () => ticketQueryOptions.detail(tenantId, ticketId),
    [tenantId, ticketId]
  );

  // Try to get placeholder data from list cache
  const placeholderData = useMemo(() => {
    if (options?.placeholderData) return options.placeholderData;

    // Try to find ticket in list cache
    const listQueries = queryClient.getQueriesData({
      queryKey: ['tickets', 'list', tenantId],
    });

    for (const [, data] of listQueries) {
      if (Array.isArray(data)) {
        const ticket = data.find((t: Ticket) => t.id === ticketId);
        if (ticket) return ticket;
      }
    }

    return undefined;
  }, [queryClient, tenantId, ticketId, options?.placeholderData]);

  // Memoize selector function
  const selector = useCallback(
    (data: Ticket) => {
      if (options?.select) {
        return options.select(data);
      }
      return data;
    },
    [options?.select]
  );

  return useQuery({
    ...queryOptions,
    select: selector,
    enabled: options?.enabled !== false && !!tenantId && !!ticketId,
    placeholderData,
  });
}

/**
 * Optimized user search hook with debouncing and caching
 */
export function useOptimizedUserSearch(
  tenantId: string,
  query: string,
  options?: {
    limit?: number;
    select?: (data: User[]) => User[];
    enabled?: boolean;
  }
) {
  // Memoize query options
  const queryOptions = useMemo(
    () => userQueryOptions.search(tenantId, query, options?.limit),
    [tenantId, query, options?.limit]
  );

  // Memoize selector function
  const selector = useCallback(
    (data: User[]) => {
      if (options?.select) {
        return options.select(data);
      }
      return data;
    },
    [options?.select]
  );

  return useQuery({
    ...queryOptions,
    select: selector,
    enabled: options?.enabled !== false && query.length >= 3,
  });
}

/**
 * Optimized tenant resolution hook
 */
export function useOptimizedTenantResolve(
  subdomain: string,
  options?: {
    select?: <T>(data: Tenant) => T;
    enabled?: boolean;
  }
) {
  // Memoize query options
  const queryOptions = useMemo(
    () => tenantQueryOptions.resolve(subdomain),
    [subdomain]
  );

  // Memoize selector function
  const selector = useCallback(
    (data: Tenant) => {
      if (options?.select) {
        return options.select(data);
      }
      return data;
    },
    [options?.select]
  );

  return useQuery({
    ...queryOptions,
    select: selector,
    enabled: options?.enabled !== false && !!subdomain,
  });
}

/**
 * Advanced query hook with optimistic updates support
 */
export function useOptimisticQuery<TData, TError = Error>(
  queryOptions: UseQueryOptions<TData, TError>,
  optimisticUpdate?: {
    updateFn: (oldData: TData | undefined) => TData;
    revertFn?: (oldData: TData | undefined) => TData;
  }
) {
  const queryClient = useQueryClient();

  const query = useQuery(queryOptions);

  // Optimistic update function
  const setOptimisticData = useCallback(
    (updateFn: (oldData: TData | undefined) => TData) => {
      queryClient.setQueryData(queryOptions.queryKey!, updateFn);
    },
    [queryClient, queryOptions.queryKey]
  );

  // Revert optimistic update
  const revertOptimisticData = useCallback(() => {
    if (optimisticUpdate?.revertFn) {
      queryClient.setQueryData(
        queryOptions.queryKey!,
        optimisticUpdate.revertFn
      );
    } else {
      // Refetch to get server state
      queryClient.invalidateQueries({ queryKey: queryOptions.queryKey });
    }
  }, [queryClient, queryOptions.queryKey, optimisticUpdate?.revertFn]);

  return {
    ...query,
    setOptimisticData,
    revertOptimisticData,
  };
}

/**
 * Enhanced query invalidation utilities
 */
export const queryInvalidationUtils = {
  /**
   * Invalidate all ticket-related queries for a tenant
   */
  invalidateTickets: (
    queryClient: ReturnType<typeof useQueryClient>,
    tenantId: string
  ) => {
    queryClient.invalidateQueries({
      queryKey: ['tickets', 'list', tenantId],
    });
    queryClient.invalidateQueries({
      queryKey: ['tickets', 'detail', tenantId],
    });
  },

  /**
   * Invalidate specific ticket detail
   */
  invalidateTicketDetail: (
    queryClient: ReturnType<typeof useQueryClient>,
    tenantId: string,
    ticketId: string
  ) => {
    queryClient.invalidateQueries({
      queryKey: ['tickets', 'detail', tenantId, ticketId],
    });
  },

  /**
   * Update ticket in cache without refetch
   */
  updateTicketInCache: (
    queryClient: ReturnType<typeof useQueryClient>,
    tenantId: string,
    ticketId: string,
    updateFn: (oldTicket: Ticket) => Ticket
  ) => {
    // Update in detail cache
    queryClient.setQueryData(
      ['tickets', 'detail', tenantId, ticketId],
      updateFn
    );

    // Update in list cache
    queryClient.setQueriesData(
      { queryKey: ['tickets', 'list', tenantId] },
      (oldData: Ticket[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map((ticket) =>
          ticket.id === ticketId ? updateFn(ticket) : ticket
        );
      }
    );
  },

  /**
   * Optimized cache warming for ticket details
   */
  warmTicketDetailCache: (
    queryClient: ReturnType<typeof useQueryClient>,
    tenantId: string,
    tickets: Ticket[]
  ) => {
    tickets.forEach((ticket) => {
      queryClient.setQueryData(
        ['tickets', 'detail', tenantId, ticket.id],
        ticket
      );
    });
  },
};

/**
 * Performance monitoring hook for queries
 */
export function useQueryPerformance(queryKey: string[]) {
  const queryClient = useQueryClient();

  return useMemo(() => {
    const query = queryClient.getQueryState(queryKey);

    return {
      isFetching: query?.isFetching ?? false,
      isStale: query?.isStale ?? false,
      dataUpdatedAt: query?.dataUpdatedAt ?? 0,
      errorUpdatedAt: query?.errorUpdatedAt ?? 0,
      fetchFailureCount: query?.fetchFailureCount ?? 0,
      fetchFailureReason: query?.fetchFailureReason,
    };
  }, [queryClient, queryKey]);
}
