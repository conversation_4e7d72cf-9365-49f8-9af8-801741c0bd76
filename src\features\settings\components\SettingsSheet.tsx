'use client';

import { useEffect } from 'react';
import {
  Sheet,
  Sheet<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  Sheet<PERSON>itle,
  SheetDescription,
} from '@/features/shared/components/ui/sheet';
import { ScrollArea } from '@/features/shared/components/ui/scroll-area';
import { Separator } from '@/features/shared/components/ui/separator';
import { Badge } from '@/features/shared/components/ui/badge';
import { useAuth } from '@/features/shared/hooks/useAuth';
import { useSettingsUI } from '../hooks/useSettingsSync';
import { SettingsNavigation } from './SettingsNavigation';
import { SettingsContent } from './SettingsContent';
import { cn } from '@/lib/utils';
import { SectionErrorBoundary } from '@/lib/error-management';
// Legacy settings store removed - React Query handles state

interface SettingsSheetProps {
  className?: string;
}

export function SettingsSheet({ className }: SettingsSheetProps) {
  const { user, role, isAdmin, isSuperAdmin } = useAuth();
  const { isSettingsOpen, closeSettings, activeSection } = useSettingsUI();
  // Legacy isLoading removed - React Query handles loading states
  const isLoading = false; // React Query handles loading states per component

  const hasAdminAccess = isAdmin || isSuperAdmin;

  // Close settings when user signs out
  useEffect(() => {
    if (!user && isSettingsOpen) {
      closeSettings();
    }
  }, [user, isSettingsOpen, closeSettings]);

  return (
    <Sheet
      open={Boolean(isSettingsOpen)}
      onOpenChange={(open) => !open && closeSettings()}
    >
      <SheetContent
        side='right'
        className={cn(
          'w-full sm:max-w-2xl lg:max-w-3xl xl:max-w-5xl',
          'flex flex-col h-full',
          'bg-background text-foreground border-border',
          className
        )}
      >
        <SheetHeader className='flex-shrink-0 pb-4'>
          <div className='flex items-center justify-between'>
            <div>
              <SheetTitle className='text-xl font-semibold'>
                Settings
              </SheetTitle>
              <SheetDescription className='text-sm text-muted-foreground mt-1'>
                Manage your account settings and preferences
              </SheetDescription>
            </div>
            <div className='flex items-center gap-2'>
              <Badge variant='outline' className='text-xs'>
                {role === 'super_admin'
                  ? 'Super Admin'
                  : role === 'admin'
                    ? 'Admin'
                    : role === 'agent'
                      ? 'Agent'
                      : 'User'}
              </Badge>
            </div>
          </div>
        </SheetHeader>

        <Separator className='flex-shrink-0' />

        <div className='flex flex-1 min-h-0 mt-4'>
          {/* Navigation Sidebar */}
          <div className='flex-shrink-0 w-64 pr-6'>
            <SettingsNavigation
              activeSection={String(activeSection || 'general')}
              hasAdminAccess={hasAdminAccess}
            />
          </div>

          {/* Vertical Separator */}
          <Separator orientation='vertical' className='flex-shrink-0' />

          {/* Main Content Area */}
          <div className='flex-1 pl-6 min-w-0'>
            <ScrollArea className='h-full'>
              <div className='pr-4'>
                <SectionErrorBoundary context='settings'>
                  {isLoading ? (
                    <div className='space-y-4'>
                      <div className='h-4 bg-gray-200 rounded animate-pulse' />
                      <div className='h-4 bg-gray-200 rounded animate-pulse w-3/4' />
                      <div className='h-4 bg-gray-200 rounded animate-pulse w-1/2' />
                    </div>
                  ) : (
                    <SettingsContent
                      activeSection={String(activeSection || 'general')}
                      hasAdminAccess={hasAdminAccess}
                    />
                  )}
                </SectionErrorBoundary>
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Performance indicator for development */}
        <SettingsPerformanceIndicator
          isLoading={false}
          loadTime={performance.now()}
        />
      </SheetContent>
    </Sheet>
  );
}
