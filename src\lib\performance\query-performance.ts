/**
 * React Query Performance Monitoring - 2025 Optimized
 * 
 * Enhanced performance tracking for React Query operations
 * Integrates with the global performance monitor
 */

import { QueryClient, QueryCache, MutationCache } from '@tanstack/react-query';
import { performanceMonitor } from './performance-monitor';

/**
 * Enhanced QueryClient with performance monitoring
 */
export function createOptimizedQueryClient(): QueryClient {
  const queryCache = new QueryCache({
    onSuccess: (data, query) => {
      const queryKey = JSON.stringify(query.queryKey);
      const dataSize = JSON.stringify(data).length;
      const fetchTime = query.state.dataUpdatedAt - (query.state.fetchFailureCount > 0 ? 0 : query.state.dataUpdatedAt);
      
      performanceMonitor.recordQuery(
        queryKey,
        fetchTime || 0,
        query.state.status === 'success' && query.state.isFetching === false,
        dataSize
      );
    },
    onError: (error, query) => {
      const queryKey = JSON.stringify(query.queryKey);
      performanceMonitor.recordMetric(
        `query_error_${queryKey}`,
        0,
        'query',
        { error: String(error) }
      );
    },
  });

  const mutationCache = new MutationCache({
    onSuccess: (data, variables, context, mutation) => {
      const mutationKey = mutation.options.mutationKey ? JSON.stringify(mutation.options.mutationKey) : 'unknown';
      performanceMonitor.recordMetric(
        `mutation_success_${mutationKey}`,
        Date.now() - (mutation.state.submittedAt || Date.now()),
        'mutation'
      );
    },
    onError: (error, variables, context, mutation) => {
      const mutationKey = mutation.options.mutationKey ? JSON.stringify(mutation.options.mutationKey) : 'unknown';
      performanceMonitor.recordMetric(
        `mutation_error_${mutationKey}`,
        Date.now() - (mutation.state.submittedAt || Date.now()),
        'mutation',
        { error: String(error) }
      );
    },
  });

  return new QueryClient({
    queryCache,
    mutationCache,
    defaultOptions: {
      queries: {
        // Optimized caching strategy
        staleTime: 30 * 60 * 1000, // 30 minutes
        gcTime: 60 * 60 * 1000, // 1 hour
        
        // Enhanced retry logic with performance tracking
        retry: (failureCount, error: Error & { status?: number; code?: number }) => {
          performanceMonitor.recordMetric(
            'query_retry',
            failureCount,
            'query',
            { error: String(error) }
          );
          
          // Don't retry auth errors
          if (error?.status === 401 || error?.code === 401) {
            return false;
          }
          
          return failureCount < 2;
        },
        
        // Optimized retry delay with exponential backoff
        retryDelay: (attemptIndex) => {
          const delay = Math.min(500 * 2 ** attemptIndex, 5000);
          performanceMonitor.recordMetric(
            'query_retry_delay',
            delay,
            'query'
          );
          return delay;
        },
        
        // Performance-optimized refetch settings
        refetchOnWindowFocus: true,
        refetchOnMount: true,
        refetchOnReconnect: true,
        networkMode: 'online',
        
        // Enhanced error handling
        throwOnError: false,
        
        // Optimized for React 19
        notifyOnChangeProps: 'all',
      },
      mutations: {
        // Optimized mutation retry logic
        retry: (failureCount, error: Error & { status?: number; code?: number }) => {
          performanceMonitor.recordMetric(
            'mutation_retry',
            failureCount,
            'mutation',
            { error: String(error) }
          );
          
          // Don't retry auth errors
          if (error?.status === 401 || error?.code === 401) {
            return false;
          }
          
          return failureCount < 1;
        },
        
        networkMode: 'online',
        throwOnError: false,
      },
    },
  });
}

/**
 * Query performance analyzer
 */
export class QueryPerformanceAnalyzer {
  private queryClient: QueryClient;

  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
  }

  /**
   * Analyze cache performance
   */
  analyzeCachePerformance(): {
    totalQueries: number;
    activeQueries: number;
    staleQueries: number;
    errorQueries: number;
    cacheSize: number;
  } {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    const activeQueries = queries.filter(q => q.state.status === 'pending').length;
    const staleQueries = queries.filter(q => q.isStale()).length;
    const errorQueries = queries.filter(q => q.state.status === 'error').length;
    
    // Estimate cache size
    const cacheSize = queries.reduce((size, query) => {
      return size + JSON.stringify(query.state.data).length;
    }, 0);

    return {
      totalQueries: queries.length,
      activeQueries,
      staleQueries,
      errorQueries,
      cacheSize,
    };
  }

  /**
   * Get slow queries
   */
  getSlowQueries(threshold = 1000): Array<{
    queryKey: string;
    fetchTime: number;
    status: string;
  }> {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    return queries
      .filter(query => {
        const fetchTime = query.state.dataUpdatedAt - (query.state.fetchFailureCount > 0 ? 0 : query.state.dataUpdatedAt);
        return fetchTime > threshold;
      })
      .map(query => ({
        queryKey: JSON.stringify(query.queryKey),
        fetchTime: query.state.dataUpdatedAt - (query.state.fetchFailureCount > 0 ? 0 : query.state.dataUpdatedAt),
        status: query.state.status,
      }))
      .sort((a, b) => b.fetchTime - a.fetchTime);
  }

  /**
   * Optimize cache by removing stale queries
   */
  optimizeCache(): {
    removedQueries: number;
    freedMemory: number;
  } {
    const cache = this.queryClient.getQueryCache();
    const queries = cache.getAll();
    
    let removedQueries = 0;
    let freedMemory = 0;
    
    queries.forEach(query => {
      if (query.isStale() && !query.getObserversCount()) {
        const dataSize = JSON.stringify(query.state.data).length;
        cache.remove(query);
        removedQueries++;
        freedMemory += dataSize;
      }
    });

    performanceMonitor.recordMetric(
      'cache_optimization',
      removedQueries,
      'query',
      { freedMemory }
    );

    return { removedQueries, freedMemory };
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    hitRate: number;
    missRate: number;
    averageFetchTime: number;
    totalCacheSize: number;
  } {
    const summary = performanceMonitor.getSummary();
    const cachePerf = this.analyzeCachePerformance();
    
    return {
      hitRate: summary.cacheHitRate,
      missRate: 100 - summary.cacheHitRate,
      averageFetchTime: summary.averageQueryTime,
      totalCacheSize: cachePerf.cacheSize,
    };
  }
}

/**
 * React Query DevTools performance integration
 */
export function setupQueryDevTools(queryClient: QueryClient): void {
  if (process.env.NODE_ENV === 'development') {
    // Add performance monitoring to DevTools
    const analyzer = new QueryPerformanceAnalyzer(queryClient);
    
    // Log performance stats every 30 seconds in development
    setInterval(() => {
      const stats = analyzer.getCacheStats();
      console.group('🔍 React Query Performance Stats');
      console.log('Cache Hit Rate:', `${stats.hitRate.toFixed(1)}%`);
      console.log('Average Fetch Time:', `${stats.averageFetchTime.toFixed(2)}ms`);
      console.log('Total Cache Size:', `${(stats.totalCacheSize / 1024).toFixed(2)}KB`);
      console.groupEnd();
    }, 30000);
  }
}

/**
 * Query key optimization utilities
 */
export const queryKeyUtils = {
  /**
   * Create optimized query key with tenant isolation
   */
  createTenantKey: (tenantId: string, ...parts: (string | number)[]): string[] => {
    return ['tenant', tenantId, ...parts.map(String)];
  },

  /**
   * Create optimized query key for real-time data
   */
  createRealtimeKey: (tenantId: string, table: string, ...filters: string[]): string[] => {
    return ['realtime', tenantId, table, ...filters];
  },

  /**
   * Validate query key structure
   */
  validateKey: (key: unknown[]): boolean => {
    return Array.isArray(key) && key.length > 0 && key.every(part => 
      typeof part === 'string' || typeof part === 'number'
    );
  },
};
